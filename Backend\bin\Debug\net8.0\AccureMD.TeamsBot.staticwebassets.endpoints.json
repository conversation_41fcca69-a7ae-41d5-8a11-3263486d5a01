{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "css/teams-app.css", "AssetFile": "css/teams-app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "11424"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0b3Vytiq3sWD55HdmkfCBjn39BgFGuxMJFo7b1oziNQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 19:01:32 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0b3Vytiq3sWD55HdmkfCBjn39BgFGuxMJFo7b1oziNQ="}]}, {"Route": "css/teams-app.vwl5012ydz.css", "AssetFile": "css/teams-app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "11424"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0b3Vytiq3sWD55HdmkfCBjn39BgFGuxMJFo7b1oziNQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 19:01:32 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vwl5012ydz"}, {"Name": "integrity", "Value": "sha256-0b3Vytiq3sWD55HdmkfCBjn39BgFGuxMJFo7b1oziNQ="}, {"Name": "label", "Value": "css/teams-app.css"}]}, {"Route": "html/auth-callback.drq4formlr.html", "AssetFile": "html/auth-callback.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "17666"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"h6D7MxseOwe6r+AISkd/NlRrL7LCW8lqkHoN3HOBKio=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 20:14:05 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "drq4formlr"}, {"Name": "integrity", "Value": "sha256-h6D7MxseOwe6r+AISkd/NlRrL7LCW8lqkHoN3HOBKio="}, {"Name": "label", "Value": "html/auth-callback.html"}]}, {"Route": "html/auth-callback.html", "AssetFile": "html/auth-callback.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "17666"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"h6D7MxseOwe6r+AISkd/NlRrL7LCW8lqkHoN3HOBKio=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 20:14:05 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-h6D7MxseOwe6r+AISkd/NlRrL7LCW8lqkHoN3HOBKio="}]}, {"Route": "html/auth-start.html", "AssetFile": "html/auth-start.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5567"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7s42FjuMVMUg5i4L4V/DQ1ZnhSvl1nmF+vMTgUl89sk=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 20:07:03 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7s42FjuMVMUg5i4L4V/DQ1ZnhSvl1nmF+vMTgUl89sk="}]}, {"Route": "html/auth-start.uorbu8vuak.html", "AssetFile": "html/auth-start.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5567"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7s42FjuMVMUg5i4L4V/DQ1ZnhSvl1nmF+vMTgUl89sk=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 20:07:03 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uorbu8vuak"}, {"Name": "integrity", "Value": "sha256-7s42FjuMVMUg5i4L4V/DQ1ZnhSvl1nmF+vMTgUl89sk="}, {"Name": "label", "Value": "html/auth-start.html"}]}, {"Route": "html/configure.8inynrhuf2.html", "AssetFile": "html/configure.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "4092"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 17:45:39 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inynrhuf2"}, {"Name": "integrity", "Value": "sha256-gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU="}, {"Name": "label", "Value": "html/configure.html"}]}, {"Route": "html/configure.html", "AssetFile": "html/configure.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "4092"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 17:45:39 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU="}]}, {"Route": "html/index.h6nvgw2bxq.html", "AssetFile": "html/index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6691"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"GfcoGCssHEosZ5jXb6bdeIVquPUcwyOkuAeHCeKM3Hk=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 20:14:05 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h6nvgw2bxq"}, {"Name": "integrity", "Value": "sha256-GfcoGCssHEosZ5jXb6bdeIVquPUcwyOkuAeHCeKM3Hk="}, {"Name": "label", "Value": "html/index.html"}]}, {"Route": "html/index.html", "AssetFile": "html/index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6691"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"GfcoGCssHEosZ5jXb6bdeIVquPUcwyOkuAeHCeKM3Hk=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 20:14:05 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GfcoGCssHEosZ5jXb6bdeIVquPUcwyOkuAeHCeKM3Hk="}]}, {"Route": "html/privacy.9ukuo7vfri.html", "AssetFile": "html/privacy.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1322"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9ukuo7vfri"}, {"Name": "integrity", "Value": "sha256-AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s="}, {"Name": "label", "Value": "html/privacy.html"}]}, {"Route": "html/privacy.html", "AssetFile": "html/privacy.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1322"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s="}]}, {"Route": "html/termsofuse.html", "AssetFile": "html/termsofuse.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1168"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:34 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY="}]}, {"Route": "html/termsofuse.tv39flyyfq.html", "AssetFile": "html/termsofuse.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1168"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:34 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tv39flyyfq"}, {"Name": "integrity", "Value": "sha256-FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY="}, {"Name": "label", "Value": "html/termsofuse.html"}]}, {"Route": "js/teams-app.3t8nsn9021.js", "AssetFile": "js/teams-app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qYv/YHYdU/TR40tHZOqoa8tXSVkmPBWr6VnaSpYfVyg=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 20:43:58 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3t8nsn9021"}, {"Name": "integrity", "Value": "sha256-qYv/YHYdU/TR40tHZOqoa8tXSVkmPBWr6VnaSpYfVyg="}, {"Name": "label", "Value": "js/teams-app.js"}]}, {"Route": "js/teams-app.js", "AssetFile": "js/teams-app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15071"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"qYv/YHYdU/TR40tHZOqoa8tXSVkmPBWr6VnaSpYfVyg=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 20:43:58 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qYv/YHYdU/TR40tHZOqoa8tXSVkmPBWr6VnaSpYfVyg="}]}, {"Route": "teams-test.cztlu2xxj7.html", "AssetFile": "teams-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6390"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"+P5zgdWxWZ2bZZMeuIi7vJMm+530R8iVicKn/7LsAgo=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 19:44:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cztlu2xxj7"}, {"Name": "integrity", "Value": "sha256-+P5zgdWxWZ2bZZMeuIi7vJMm+530R8iVicKn/7LsAgo="}, {"Name": "label", "Value": "teams-test.html"}]}, {"Route": "teams-test.html", "AssetFile": "teams-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6390"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"+P5zgdWxWZ2bZZMeuIi7vJMm+530R8iVicKn/7LsAgo=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 19:44:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+P5zgdWxWZ2bZZMeuIi7vJMm+530R8iVicKn/7LsAgo="}]}, {"Route": "test-auth.2r3ito90zx.html", "AssetFile": "test-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2140"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 11:50:16 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2r3ito90zx"}, {"Name": "integrity", "Value": "sha256-fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o="}, {"Name": "label", "Value": "test-auth.html"}]}, {"Route": "test-auth.html", "AssetFile": "test-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2140"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 11:50:16 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o="}]}, {"Route": "test-teams-auth.html", "AssetFile": "test-teams-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "9705"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"zxsIQCwlwqYJYejoAnllGdxDbA0/p/akEX/n4Ydb17I=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 20:45:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zxsIQCwlwqYJYejoAnllGdxDbA0/p/akEX/n4Ydb17I="}]}, {"Route": "test-teams-auth.r54zswcdn2.html", "AssetFile": "test-teams-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "9705"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"zxsIQCwlwqYJYejoAnllGdxDbA0/p/akEX/n4Ydb17I=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 20:45:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r54zswcdn2"}, {"Name": "integrity", "Value": "sha256-zxsIQCwlwqYJYejoAnllGdxDbA0/p/akEX/n4Ydb17I="}, {"Name": "label", "Value": "test-teams-auth.html"}]}]}